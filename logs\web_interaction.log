【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【程序退出】
【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
【web_engine.py】-【INFO】-【检测到登出状态，文本匹配: '登录'】
【web_engine.py】-【INFO】-【检测到登出状态，文本匹配: '登录'】
【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
【web_engine.py】-【INFO】-【检测到登出状态，文本匹配: '登录'】
【web_engine.py】-【INFO】-【检测到登出状态，文本匹配: '登录'】
【web_engine.py】-【INFO】-【页面加载完成】
【web_engine.py】-【INFO】-【检查登录状态...】
【web_engine.py】-【INFO】-【检测到登出状态，文本匹配: '登录'】
【web_engine.py】-【WARNING】-【检测到未登录状态】
【web_engine.py】-【INFO】-【处理登录提醒流程】
【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
【web_engine.py】-【INFO】-【消息监控已启动】
【web_engine.py】-【INFO】-【登录状态监控已启动】
【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【程序退出】
【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
【web_engine.py】-【INFO】-【页面加载完成】
【web_engine.py】-【INFO】-【检查登录状态...】
【web_engine.py】-【INFO】-【无法确定登录状态】
【web_engine.py】-【INFO】-【消息监控已启动】
【web_engine.py】-【INFO】-【登录状态监控已启动】
【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
【web_engine.py】-【ERROR】-【页面或配置未初始化】
【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【正在等待 2 个任务完成取消...】
【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【程序退出】
【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
【web_engine.py】-【ERROR】-【导航失败: Target page, context or browser has been closed】
【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
【web_engine.py】-【INFO】-【页面加载完成】
【web_engine.py】-【INFO】-【检查登录状态...】
【web_engine.py】-【INFO】-【无法确定登录状态】
【web_engine.py】-【INFO】-【消息监控已启动】
【web_engine.py】-【INFO】-【登录状态监控已启动】
【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
【web_engine.py】-【ERROR】-【页面或配置未初始化】
【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【正在等待 2 个任务完成取消...】
【main.py】-【INFO】-【应用程序即将退出】
【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:27:55
================================================================================
2025-06-12 17:27:55-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:27:55-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:27:55-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:27:57-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:27:57-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:27:57-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:30:00-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-12 17:30:00-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-12 17:30:37-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-12 17:30:37-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-12 17:30:47-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-12 17:30:47-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-12 17:30:47-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-12 17:31:20-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:31:20-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:31:20-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:31:20-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:31:20-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:31:20-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:32:15
================================================================================
2025-06-12 17:32:15-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:32:15-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:32:15-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:32:18-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:32:18-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:32:18-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】

================================================================================
新会话开始于: 2025-06-12 17:34:40
================================================================================
2025-06-12 17:34:40-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:34:40-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:34:40-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:34:42-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:34:42-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:34:42-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:34:58-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:34:58-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:34:59-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:34:59-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:34:59-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:34:59-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:37:12
================================================================================
2025-06-12 17:37:12-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:37:12-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:37:12-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:37:15-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:37:15-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:37:15-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:37:19-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:37:19-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:37:19-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:37:19-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:37:19-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:37:19-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:39:47
================================================================================
2025-06-12 17:39:47-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:39:47-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:39:47-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:39:50-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:39:50-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:39:50-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:40:00-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:40:00-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:40:01-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:40:01-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:40:01-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:40:01-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:42:27
================================================================================
2025-06-12 17:42:27-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:42:27-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:42:27-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:42:30-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:42:30-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:42:35-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:42:36-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:42:36-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:42:36-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:42:36-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:42:36-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 17:47:24
================================================================================
2025-06-12 17:47:24-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 17:47:24-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 17:47:24-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 17:47:27-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 17:47:27-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 17:47:30-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-12 17:47:30-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-12 17:48:11-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-12 17:48:11-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-12 17:48:21-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-12 17:48:21-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-12 17:48:21-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-12 17:53:12-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 17:53:12-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 17:53:12-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 17:53:12-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 17:53:12-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 17:53:12-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-12 20:17:06
================================================================================
2025-06-12 20:17:06-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-12 20:17:06-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-12 20:17:06-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-12 20:17:13-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 20:17:13-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-12 20:17:16-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-12 20:17:16-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-12 20:17:27-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-12 20:17:27-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-12 20:17:37-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-12 20:17:37-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-12 20:17:37-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-12 20:17:39-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 20:17:39-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 20:17:40-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-12 20:17:42-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-12 20:17:42-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-12 20:17:42-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-12 20:17:42-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-12 20:17:42-【main.py】-【INFO】-【应用程序即将退出】
2025-06-12 20:17:42-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 10:33:38
================================================================================
2025-06-13 10:33:38-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 10:33:38-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 10:33:38-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 10:33:41-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 10:33:41-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 10:33:52-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 10:33:52-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 10:34:13-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 10:34:13-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 10:34:23-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-13 10:34:23-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 10:34:23-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 12:35:43-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 12:35:43-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:35:43-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:35:43-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 12:35:43-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 12:35:43-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 12:36:53
================================================================================
2025-06-13 12:36:53-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 12:36:53-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 12:36:53-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 12:36:55-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:36:55-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 12:37:02-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 12:37:02-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 12:37:02-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 12:39:02-【web_engine.py】-【ERROR】-【导航失败: Timeout 120000ms exceeded.】
2025-06-13 12:40:13-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 12:40:13-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:40:14-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:40:14-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 12:40:14-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 12:40:14-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 12:40:33
================================================================================
2025-06-13 12:40:33-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 12:40:33-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 12:40:33-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 12:40:35-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:40:35-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 12:40:59-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 12:40:59-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 12:40:59-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 12:41:26-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-13 12:41:26-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 12:41:26-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 12:41:40-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:41:40-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:41:41-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:41:42-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 12:41:42-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:41:43-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:41:43-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 12:41:43-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 12:41:43-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 12:55:39
================================================================================
2025-06-13 12:55:39-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 12:55:39-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 12:55:39-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 12:55:41-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:55:41-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 12:55:41-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 12:55:41-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 12:55:41-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 12:56:16-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-13 12:56:16-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 12:56:16-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 12:58:07-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:58:07-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:58:08-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:58:10-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 12:58:10-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:58:10-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:58:10-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 12:58:10-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 12:58:10-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 12:59:20
================================================================================
2025-06-13 12:59:20-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 12:59:20-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 12:59:20-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 12:59:21-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 12:59:21-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 12:59:23-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 12:59:23-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 12:59:23-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 12:59:53-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 12:59:53-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 12:59:53-【web_engine.py】-【ERROR】-【导航失败: Target page, context or browser has been closed】
2025-06-13 12:59:54-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 12:59:54-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 12:59:54-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 12:59:54-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 13:18:50
================================================================================
2025-06-13 13:18:50-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 13:18:50-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 13:18:50-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 13:18:52-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:18:52-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 13:18:54-【web_engine.py】-【INFO】-【📁 登录状态文件不存在: storage\豆包ai聊天\doubao_cookies.json】
2025-06-13 13:18:55-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 13:18:55-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 13:18:55-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 13:19:32-【web_engine.py】-【WARNING】-【检测到未登录状态】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【处理登录提醒流程】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【确认检测到登录按钮，文本: '登录'】
2025-06-13 13:19:32-【web_engine.py】-【WARNING】-【⚠️  检测到需要登录，请手动登录后继续操作】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 13:19:32-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 13:20:37-【web_engine.py】-【INFO】-【登录状态发生变化: 未登录 -> 已登录】
2025-06-13 13:20:38-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665205 字节)】
2025-06-13 13:21:02-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:21:02-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 13:21:02-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665207 字节)】
2025-06-13 13:21:02-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:21:03-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 13:21:03-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:21:03-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:21:03-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 13:21:03-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 13:21:03-【main.py】-【INFO】-【正在等待 3 个任务完成取消...】
2025-06-13 13:21:03-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 13:21:03-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 13:21:53
================================================================================
2025-06-13 13:21:53-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 13:21:53-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 13:21:53-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 13:21:54-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:21:54-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 13:21:56-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (665207 字节)】
2025-06-13 13:21:57-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 13:21:57-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 13:21:57-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 13:23:57-【web_engine.py】-【ERROR】-【导航失败: Timeout 120000ms exceeded.】
2025-06-13 13:25:37-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 13:25:37-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:25:38-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:25:38-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 13:25:38-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 13:25:38-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 13:26:44
================================================================================
2025-06-13 13:26:44-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 13:26:44-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 13:26:44-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 13:26:46-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:26:46-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 13:26:48-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (665207 字节)】
2025-06-13 13:26:49-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 13:26:49-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 13:26:49-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665182 字节)】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 13:27:30-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 13:27:31-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 13:34:51-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:34:51-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 13:34:51-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665181 字节)】
2025-06-13 13:34:52-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:34:53-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:34:59-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (665181 字节)】
2025-06-13 13:34:59-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 13:34:59-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 13:34:59-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665182 字节)】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 13:35:27-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 13:55:09-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:55:09-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 13:55:09-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (665227 字节)】
2025-06-13 13:55:09-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:55:11-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:55:12-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 13:55:12-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 13:55:12-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 13:55:12-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 13:55:12-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 13:55:12-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 13:56:26
================================================================================
2025-06-13 13:56:26-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 13:56:26-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 13:56:26-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 13:56:27-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 13:56:27-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 13:56:28-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (665227 字节)】
2025-06-13 13:56:29-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 13:56:29-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 13:56:29-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (666960 字节)】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 13:56:48-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 13:57:00-【web_engine.py】-【INFO】-【使用网站特定流程发送消息】
2025-06-13 13:57:00-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 13:57:00-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 13:57:00-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 13:57:00-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 13:57:00-【doubao_workflow.py】-【INFO】-【深度思考未启用，正在设置...】
2025-06-13 13:57:05-【doubao_workflow.py】-【WARNING】-【点击深度思考按钮失败】
2025-06-13 13:57:05-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你是豆包么...】
2025-06-13 13:57:05-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 13:57:05-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 14:18:41-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:18:41-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 14:18:42-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667157 字节)】
2025-06-13 14:18:42-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:18:43-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:18:45-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 14:18:45-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:18:46-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:18:46-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 14:18:46-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 14:18:46-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 14:18:53
================================================================================
2025-06-13 14:18:53-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 14:18:53-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 14:18:53-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 14:18:55-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:18:55-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 14:18:57-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667157 字节)】
2025-06-13 14:18:58-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 14:18:58-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 14:18:58-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 14:19:16-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 14:19:16-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667073 字节)】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 14:19:17-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 14:19:28-【web_engine.py】-【INFO】-【使用网站特定流程发送消息】
2025-06-13 14:19:28-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 14:19:29-【doubao_workflow.py】-【WARNING】-【未找到深度思考按钮元素】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 今天是几号...】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 14:19:29-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 14:30:48-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:30:48-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 14:30:49-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667178 字节)】
2025-06-13 14:30:49-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:30:50-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:30:51-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 14:30:51-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:30:51-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:30:51-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 14:30:51-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 14:30:51-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 14:30:56
================================================================================
2025-06-13 14:30:56-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 14:30:56-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 14:30:56-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 14:30:57-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:30:57-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 14:31:02-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667178 字节)】
2025-06-13 14:31:02-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 14:31:02-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 14:31:02-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 14:31:19-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 14:31:19-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 14:31:19-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 14:31:19-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 14:31:19-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 14:31:20-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667203 字节)】
2025-06-13 14:31:20-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 14:31:20-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 14:31:20-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 14:31:20-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 14:31:29-【web_engine.py】-【INFO】-【使用网站特定流程发送消息】
2025-06-13 14:31:29-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 14:31:30-【doubao_workflow.py】-【ERROR】-【设置深度思考模式失败: 'str' object has no attribute 'query_selector'】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 今天天气怎样...】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 14:31:30-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 14:33:54-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:33:54-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 14:33:54-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667259 字节)】
2025-06-13 14:33:55-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:33:56-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:33:56-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 14:33:56-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 14:33:56-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 14:33:56-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 14:33:56-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 14:33:56-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 14:36:26
================================================================================
2025-06-13 14:36:26-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 14:36:26-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 14:36:26-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 14:36:28-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 14:36:28-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 14:36:29-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667259 字节)】
2025-06-13 14:36:30-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 14:36:30-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 14:36:30-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667204 字节)】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 14:36:55-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 14:37:33-【web_engine.py】-【INFO】-【使用网站特定流程发送消息】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 14:37:33-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你的训练材料来源于什么...】
2025-06-13 14:37:34-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 14:37:34-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 15:09:39-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 15:09:39-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 15:09:40-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667174 字节)】
2025-06-13 15:09:40-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 15:09:41-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 15:09:42-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 15:09:42-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 15:09:43-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 15:09:43-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 15:09:43-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 15:09:43-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 15:09:50
================================================================================
2025-06-13 15:09:50-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 15:09:50-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 15:09:50-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 15:09:51-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 15:09:51-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 15:09:53-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667174 字节)】
2025-06-13 15:09:54-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 15:09:54-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 15:09:54-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667129 字节)】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 15:10:23-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 15:10:30-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 15:10:30-【doubao_workflow.py】-【ERROR】-【发送消息并获取回复失败: 'DoubaoWorkflow' object has no attribute '_ensure_new_conversation'】

================================================================================
新会话开始于: 2025-06-13 15:14:06
================================================================================
2025-06-13 15:14:06-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 15:14:06-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 15:14:06-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 15:14:07-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 15:14:07-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 15:14:10-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667129 字节)】
2025-06-13 15:14:11-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 15:14:11-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 15:14:11-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667129 字节)】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 15:14:53-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 15:15:11-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你可以协助做化学研究么...】
2025-06-13 15:15:11-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 15:15:13-【doubao_workflow.py】-【INFO】-【步骤1: 定位消息列表...】
2025-06-13 15:15:13-【doubao_workflow.py】-【ERROR】-【步骤1失败: 未找到消息列表】
2025-06-13 15:15:13-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 15:17:44-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 15:17:44-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 15:17:44-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667185 字节)】
2025-06-13 15:17:45-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 15:17:46-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 15:18:39-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 15:18:39-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 15:18:39-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 15:18:39-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 15:18:39-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 15:18:39-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 15:23:11
================================================================================
2025-06-13 15:23:11-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 15:23:11-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 15:23:11-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 15:23:13-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 15:23:14-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 15:23:19-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667185 字节)】
2025-06-13 15:23:19-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 15:23:19-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 15:23:19-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667128 字节)】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 15:23:43-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 15:24:04-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你的什么能力最强...】
2025-06-13 15:24:04-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 15:24:05-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 15:24:08-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:10-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 15:24:10-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:12-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 15:24:12-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:14-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 15:24:14-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:16-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 15:24:16-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:18-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 7】
2025-06-13 15:24:18-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:20-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 7】
2025-06-13 15:24:20-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:22-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 16】
2025-06-13 15:24:22-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:24-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 17】
2025-06-13 15:24:24-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:26-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 17】
2025-06-13 15:24:26-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:28-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 15:24:28-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:30-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:30-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:32-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:32-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:35-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:35-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:37-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:37-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:39-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:39-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:41-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:41-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:43-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:43-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:45-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:45-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:47-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:47-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:49-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:49-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:51-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:51-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:53-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:53-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:55-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:55-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:57-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:57-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:24:59-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:24:59-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:25:01-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:25:01-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:25:03-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:25:03-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:25:06-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:25:06-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:25:08-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 29】
2025-06-13 15:25:08-【doubao_workflow.py】-【INFO】-【容器存在但不是回复消息，继续等待...】
2025-06-13 15:25:10-【doubao_workflow.py】-【WARNING】-【等待回复容器超时（60秒）】
2025-06-13 15:25:10-【doubao_workflow.py】-【ERROR】-【步骤2失败: 未找到回复容器】
2025-06-13 15:25:10-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 16:15:05-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:15:05-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 16:15:06-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667229 字节)】
2025-06-13 16:15:06-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:15:06-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 16:15:06-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:15:06-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:15:06-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 16:15:06-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 16:15:06-【main.py】-【INFO】-【正在等待 3 个任务完成取消...】
2025-06-13 16:15:06-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 16:15:06-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 16:17:32
================================================================================
2025-06-13 16:17:32-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 16:17:32-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 16:17:32-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 16:17:34-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 16:17:34-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 16:17:35-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667229 字节)】
2025-06-13 16:17:36-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 16:17:36-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 16:17:36-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667128 字节)】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 16:18:28-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 16:23:01-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:23:01-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好...】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 16:23:02-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 6】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-U7uO4R chrome70-container', 包含receive_message: 是】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:05-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:07-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:09-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:11-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:11-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:11-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:11-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:12-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:14-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:16-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:18-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:20-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:22-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:23-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:25-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:27-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:29-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:31-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:33-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:35-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:36-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:38-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:40-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:42-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:44-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:46-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:48-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:48-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:48-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:48-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:48-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:49-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:51-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:53-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:55-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:23:57-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:23:59-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:24:00-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:24:02-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:24:04-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:24:06-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找消息容器，选择器: [class*="container-"]】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 19】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【检查前 5 个容器的详细信息:】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-U7uO4R chrome70-container', 包含receive_message: 否】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【  容器[2]: class='message-block-container-Fe43tF', 包含receive_message: 否】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【  容器[3]: class='container-mhps2t bg-s-color-bg-trans rounded-s-radius-s text-s-color-text-secondary s-font-base max-w-450 px-16 py-9 w-fit min-w-0', 包含receive_message: 否】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【  容器[4]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-U7uO4R chrome70-container'】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:24:08-【doubao_workflow.py】-【INFO】-【❌ 目标容器不包含receive_message，继续等待...】
2025-06-13 16:24:10-【doubao_workflow.py】-【WARNING】-【等待回复容器超时（60秒）】
2025-06-13 16:24:10-【doubao_workflow.py】-【ERROR】-【步骤2失败: 未找到回复容器】
2025-06-13 16:24:10-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 16:31:34-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:31:34-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 16:31:34-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667229 字节)】
2025-06-13 16:31:35-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:31:36-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 16:31:36-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:31:36-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:31:36-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 16:31:36-【web_engine.py】-【ERROR】-【启动浏览器引擎失败: Target page, context or browser has been closed】
2025-06-13 16:31:36-【main_window.py】-【ERROR】-【关闭浏览器失败: Target page, context or browser has been closed】
2025-06-13 16:31:36-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 16:31:36-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 16:33:54
================================================================================
2025-06-13 16:33:54-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 16:33:54-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 16:33:54-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 16:33:56-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 16:33:56-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 16:33:57-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667229 字节)】
2025-06-13 16:33:58-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 16:33:58-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 16:33:58-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667292 字节)】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 16:34:31-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 16:34:41-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:34:41-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 16:34:42-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 16:34:45-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 16:34:45-【doubao_workflow.py】-【INFO】-【等待消息列表出现... 已等待 0 秒】
2025-06-13 16:34:46-【doubao_workflow.py】-【INFO】-【等待消息列表出现... 已等待 1 秒】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【检查前 2 个容器的详细信息:】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【检查目标容器[1]是否为回复消息...】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【目标容器[1] class: 'container-UiWnzB'】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【在目标容器中查找receive_message，选择器: [data-testid="receive_message"]】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【✅ 找到回复容器！】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 16:34:47-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:34:50-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:34:53-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:34:56-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:34:59-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:02-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 16:35:02-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:05-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:08-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:11-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:14-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:17-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 16:35:17-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:20-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:24-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:35:27-【doubao_workflow.py】-【ERROR】-【周期性检查回复失败: Target page, context or browser has been closed】
2025-06-13 16:35:27-【doubao_workflow.py】-【ERROR】-【详细错误信息: Traceback (most recent call last):
  File "D:\TEST\AI-server\src\engine\workflows\doubao_workflow.py", line 531, in _wait_and_extract_reply
    self.logger.error(f"详细错误信息: {traceback.format_exc()}")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\async_api\_generated.py", line 2713, in query_selector
    await self._impl_obj.query_selector(selector=selector)
  File "C:\Python312\Lib\site-packages\playwright\_impl\_element_handle.py", line 322, in query_selector
    await self._channel.send("querySelector", dict(selector=selector))
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 509, in wrap_api_call
    return await cb()
           ^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 97, in inner_send
    result = next(iter(done)).result()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
】
2025-06-13 16:35:27-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 16:35:27-【doubao_workflow.py】-【ERROR】-【未能获取到回复】

================================================================================
新会话开始于: 2025-06-13 16:36:26
================================================================================
2025-06-13 16:36:26-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 16:36:26-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 16:36:26-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 16:36:28-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 16:36:28-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 16:36:31-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667292 字节)】
2025-06-13 16:36:31-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 16:36:32-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 16:36:32-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667291 字节)】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 16:37:09-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 16:40:00-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 16:40:00-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 16:40:03-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 16:40:04-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:07-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:10-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:13-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:16-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:19-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 16:40:19-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:22-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:25-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:28-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:31-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:34-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 16:40:34-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:37-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:40-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:43-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:46-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:49-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 16:40:49-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:52-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:55-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:40:58-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:01-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:04-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 16:41:04-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:07-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:10-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:13-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:16-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:19-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 16:41:19-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:22-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:25-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:28-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:31-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:34-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 16:41:34-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:37-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:40-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:43-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:46-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:49-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 16:41:49-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:52-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:55-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:41:59-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:42:02-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:42:05-【doubao_workflow.py】-【INFO】-【已等待 120 秒，继续等待回复完成...】
2025-06-13 16:42:05-【doubao_workflow.py】-【WARNING】-【等待回复完成超时（120秒）】
2025-06-13 16:42:05-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 16:42:05-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 16:44:52-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:44:52-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 16:44:52-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667346 字节)】
2025-06-13 16:44:53-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:44:54-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 16:44:54-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 16:44:54-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 16:44:54-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 16:44:54-【web_engine.py】-【ERROR】-【启动浏览器引擎失败: Target page, context or browser has been closed】
2025-06-13 16:44:54-【main_window.py】-【ERROR】-【关闭浏览器失败: Target page, context or browser has been closed】
2025-06-13 16:44:54-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 16:44:54-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 16:49:43
================================================================================
2025-06-13 16:49:43-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 16:49:43-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 16:49:43-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 16:49:45-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 16:49:45-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 16:49:45-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667346 字节)】
2025-06-13 16:49:46-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 16:49:46-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 16:49:46-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667292 字节)】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 16:50:15-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 16:50:17-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 16:50:17-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 下午好...】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 16:50:18-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 16:50:21-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:26-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:29-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:32-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:35-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:38-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 16:50:38-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:41-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:44-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:47-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:50-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:53-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 16:50:53-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:56-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:50:59-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:02-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:05-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:08-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 16:51:08-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:12-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:15-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:18-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:21-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:24-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 16:51:24-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:27-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:30-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:33-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:36-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:39-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 16:51:39-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:42-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:45-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:48-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:51-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:54-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 16:51:54-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:51:57-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:00-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:03-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:06-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:09-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 16:52:09-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:12-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:15-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:18-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:22-【doubao_workflow.py】-【INFO】-【等待消息文本元素出现...】
2025-06-13 16:52:25-【doubao_workflow.py】-【INFO】-【已等待 120 秒，继续等待回复完成...】
2025-06-13 16:52:25-【doubao_workflow.py】-【WARNING】-【等待回复完成超时（120秒）】
2025-06-13 16:52:25-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 16:52:25-【doubao_workflow.py】-【ERROR】-【未能获取到回复】

================================================================================
新会话开始于: 2025-06-13 17:00:58
================================================================================
2025-06-13 17:00:58-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:00:58-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:00:58-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:00:59-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:00:59-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:00:59-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:00:59-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:00:59-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:00:59-【main.py】-【INFO】-【正在等待 3 个任务完成取消...】
2025-06-13 17:00:59-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:00:59-【main.py】-【INFO】-【程序退出】
2025-06-13 17:01:04-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:01:04-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:01:04-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:01:05-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667393 字节)】
2025-06-13 17:01:05-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:01:05-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:01:05-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:01:05-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 17:01:57
================================================================================
2025-06-13 17:01:57-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:01:57-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:01:57-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:01:59-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:01:59-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:02:06-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667393 字节)】
2025-06-13 17:02:06-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:02:06-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:02:06-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667293 字节)】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:02:35-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:02:53-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:02:53-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 下午好...】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:02:54-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:02:57-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:02:57-【doubao_workflow.py】-【INFO】-【等待消息列表出现... 已等待 0 秒】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:02:58-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:02:59-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:02-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:05-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:08-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:08-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:09-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:12-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:15-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:18-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:21-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:24-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:27-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:30-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:33-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:36-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:39-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:42-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:42-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:42-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:42-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:42-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:43-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:46-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【尝试备选文本选择器...】
2025-06-13 17:03:49-【doubao_workflow.py】-【INFO】-【所有备选选择器都未找到有效内容】
2025-06-13 17:03:51-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:03:51-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:03:51-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667348 字节)】
2025-06-13 17:03:52-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:03:52-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:03:52-【doubao_workflow.py】-【ERROR】-【周期性检查回复失败: Target page, context or browser has been closed】
2025-06-13 17:03:52-【doubao_workflow.py】-【ERROR】-【详细错误信息: Traceback (most recent call last):
  File "D:\TEST\AI-server\src\engine\workflows\doubao_workflow.py", line 559, in _wait_and_extract_reply
    copy_button = await receive_message_element.query_selector(message_action_copy_selector)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\async_api\_generated.py", line 2713, in query_selector
    await self._impl_obj.query_selector(selector=selector)
  File "C:\Python312\Lib\site-packages\playwright\_impl\_element_handle.py", line 322, in query_selector
    await self._channel.send("querySelector", dict(selector=selector))
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 509, in wrap_api_call
    return await cb()
           ^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 85, in inner_send
    callback = self._connection._send_message_to_server(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 315, in _send_message_to_server
    raise self._closed_error
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
】
2025-06-13 17:03:52-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:03:52-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 17:03:53-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:03:55-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:03:55-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:03:55-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:03:55-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:03:55-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:03:55-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 17:16:35
================================================================================
2025-06-13 17:16:35-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:16:35-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:16:35-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:16:37-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:16:37-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:16:38-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667348 字节)】
2025-06-13 17:16:39-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:16:39-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:16:39-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667292 字节)】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:16:57-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:17:46-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:17:46-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:17:47-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:17:50-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:17:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:17:53-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:17:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:17:53-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:17:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:17:56-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:17:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:17:56-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:17:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:17:59-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:17:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:17:59-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:02-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:05-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:18:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:05-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:08-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:11-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:12-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:12-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:15-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:18-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:21-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:18:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:21-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:24-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:27-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:30-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:33-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:36-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:18:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:36-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:39-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:42-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:42-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:42-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:42-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:45-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:45-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:45-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:45-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:48-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:48-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:48-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:48-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:51-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 17:18:51-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:51-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:51-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:51-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:54-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:54-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:54-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:54-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:18:57-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:18:57-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:18:57-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:18:57-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:00-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:00-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:00-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:00-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:03-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:03-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:03-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:03-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:06-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 17:19:06-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:06-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:06-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:06-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:09-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:09-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:09-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:09-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:12-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:12-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:15-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:18-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:19-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:22-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 17:19:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:22-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:22-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:25-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:25-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:25-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:25-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:28-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:28-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:28-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:28-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:31-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:31-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:31-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:31-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:34-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:34-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:34-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:34-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:37-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 17:19:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:37-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:37-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:40-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:40-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:43-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:43-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:46-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:46-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:19:49-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:19:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:19:49-【doubao_workflow.py】-【INFO】-【❌ 消息文本元素未找到，等待元素出现...】
2025-06-13 17:19:52-【doubao_workflow.py】-【INFO】-【已等待 120 秒，继续等待回复完成...】
2025-06-13 17:19:52-【doubao_workflow.py】-【WARNING】-【等待回复完成超时（120秒）】
2025-06-13 17:19:52-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:19:52-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 17:21:49-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:21:49-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:21:49-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667348 字节)】
2025-06-13 17:21:49-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:21:50-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:21:51-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:21:51-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:21:51-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:21:51-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:21:51-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:21:51-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 17:24:41
================================================================================
2025-06-13 17:24:41-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:24:41-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:24:41-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:24:43-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:24:43-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:24:44-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667348 字节)】
2025-06-13 17:24:45-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:24:45-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:24:45-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:25:04-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:25:04-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667290 字节)】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:25:05-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:25:17-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 17:25:17-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:25:18-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:21-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:24-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:27-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:30-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:33-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:36-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:39-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:42-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:43-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:43-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:43-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:43-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:46-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:49-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:52-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:55-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:25:58-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:01-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:04-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:07-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:10-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:13-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:16-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:19-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:22-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 17:26:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:22-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:23-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:23-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:23-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:26-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:29-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:32-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:35-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:38-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:41-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:44-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:47-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:50-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:53-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:56-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:26:59-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:27:02-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:27:05-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:27:08-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:27:11-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:27:13-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:27:13-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:27:13-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667346 字节)】
2025-06-13 17:27:14-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:27:14-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:27:14-【doubao_workflow.py】-【ERROR】-【周期性检查回复失败: Target page, context or browser has been closed】
2025-06-13 17:27:14-【doubao_workflow.py】-【ERROR】-【详细错误信息: Traceback (most recent call last):
  File "D:\TEST\AI-server\src\engine\workflows\doubao_workflow.py", line 559, in _wait_and_extract_reply
    copy_button = await receive_message_element.query_selector(message_action_copy_selector)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\async_api\_generated.py", line 2713, in query_selector
    await self._impl_obj.query_selector(selector=selector)
  File "C:\Python312\Lib\site-packages\playwright\_impl\_element_handle.py", line 322, in query_selector
    await self._channel.send("querySelector", dict(selector=selector))
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 509, in wrap_api_call
    return await cb()
           ^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 85, in inner_send
    callback = self._connection._send_message_to_server(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 315, in _send_message_to_server
    raise self._closed_error
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
】
2025-06-13 17:27:14-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:27:14-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 17:27:15-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:27:15-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:27:16-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:27:16-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:27:16-【web_engine.py】-【ERROR】-【启动浏览器引擎失败: Target page, context or browser has been closed】
2025-06-13 17:27:16-【main_window.py】-【ERROR】-【关闭浏览器失败: Target page, context or browser has been closed】
2025-06-13 17:27:16-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:27:16-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 17:34:58
================================================================================
2025-06-13 17:34:58-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:34:59-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:34:59-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:35:01-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:35:01-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:35:02-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667346 字节)】
2025-06-13 17:35:03-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:35:03-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:35:03-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:35:18-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:35:18-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:35:18-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:35:18-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:35:18-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:35:19-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667290 字节)】
2025-06-13 17:35:19-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:35:19-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:35:19-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:35:19-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:35:31-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:35:31-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 17:35:32-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:35:34-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【找到的receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="circle-lm7olN"></div><div class="circle-lm7olN"></div></div></div></div></div>...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【❌ 在receive_message元素中没有找到复制按钮】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【❌ 在消息容器中也没有找到复制按钮】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:37-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:40-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:44-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:47-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:50-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:53-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:56-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:35:59-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:02-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:05-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:08-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:11-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:14-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:17-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:20-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:23-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:26-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:29-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:30-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:33-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:36-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:39-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 17:36:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:39-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:39-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:40-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:40-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:40-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:43-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:46-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:49-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:52-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:53-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:56-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:36:59-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:02-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:05-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:08-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:09-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:09-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:09-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:09-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:12-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:15-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:15-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:16-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:16-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:16-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:19-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:22-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:25-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:28-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:31-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:34-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:37-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【在receive_message元素内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从receive_message元素获取文本...】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【receive_message元素HTML结构: <div class="flex flex-col flex-grow max-w-full min-w-0"><div data-testid="message_content" class="flex flex-row w-full"><div class="flex items-baseline"><div class="dot-flashing-URWi_k"><div class="ci...】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【receive_message元素内容为空，继续等待...】
2025-06-13 17:37:40-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:37:43-【doubao_workflow.py】-【INFO】-【已等待 120 秒，继续等待回复完成...】
2025-06-13 17:37:43-【doubao_workflow.py】-【WARNING】-【等待回复完成超时（120秒）】
2025-06-13 17:37:43-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:37:43-【doubao_workflow.py】-【ERROR】-【未能获取到回复】

================================================================================
新会话开始于: 2025-06-13 17:45:58
================================================================================
2025-06-13 17:45:58-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:45:58-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:45:58-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:46:00-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:46:00-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:46:07-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667290 字节)】
2025-06-13 17:46:07-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:46:07-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:46:07-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:46:46-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:46:46-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667291 字节)】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:46:47-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:50:10-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:50:10-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:50:11-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-item-Adit9A" data-target-id="message-box-target-id" data-testid="union_message"><div data-testid="messa...】
2025-06-13 17:50:14-【doubao_workflow.py】-【INFO】-【❌ 在消息容器中没有找到复制按钮】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【❌ 在消息容器中没有找到文本内容元素】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:15-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:18-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:21-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:24-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:27-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:30-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:33-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:36-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:39-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:42-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:45-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:48-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:51-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:54-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:50:57-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:50:57-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:50:57-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:50:58-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:50:58-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:50:58-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:50:58-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:51:01-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:51:01-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:51:01-【doubao_workflow.py】-【ERROR】-【周期性检查回复失败: Target page, context or browser has been closed】
2025-06-13 17:51:01-【doubao_workflow.py】-【ERROR】-【详细错误信息: Traceback (most recent call last):
  File "D:\TEST\AI-server\src\engine\workflows\doubao_workflow.py", line 583, in _wait_and_extract_reply
    copy_button = await message_container_element.query_selector(message_action_copy_selector)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\async_api\_generated.py", line 2713, in query_selector
    await self._impl_obj.query_selector(selector=selector)
  File "C:\Python312\Lib\site-packages\playwright\_impl\_element_handle.py", line 322, in query_selector
    await self._channel.send("querySelector", dict(selector=selector))
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 59, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 509, in wrap_api_call
    return await cb()
           ^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\playwright\_impl\_connection.py", line 97, in inner_send
    result = next(iter(done)).result()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
】
2025-06-13 17:51:01-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:51:01-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 17:51:02-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:51:02-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:51:02-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:51:02-【web_engine.py】-【ERROR】-【❌ 保存登录状态失败: Target page, context or browser has been closed】
2025-06-13 17:51:02-【web_engine.py】-【ERROR】-【停止浏览器引擎时出错: Target page, context or browser has been closed】
2025-06-13 17:51:02-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:51:02-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:51:02-【main.py】-【INFO】-【正在等待 1 个任务完成取消...】
2025-06-13 17:51:02-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:51:02-【main.py】-【INFO】-【程序退出】

================================================================================
新会话开始于: 2025-06-13 17:55:50
================================================================================
2025-06-13 17:55:50-【logger.py】-【INFO】-【日志系统初始化完成】
2025-06-13 17:55:50-【config_manager.py】-【INFO】-【成功加载网站配置: 豆包AI聊天 (doubao)】
2025-06-13 17:55:50-【config_manager.py】-【INFO】-【配置管理器初始化完成，加载了 1 个网站配置】
2025-06-13 17:55:51-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:55:51-【config_manager.py】-【INFO】-【切换到网站: 豆包AI聊天】
2025-06-13 17:55:52-【web_engine.py】-【INFO】-【✅ 加载已保存的登录状态: storage\豆包ai聊天\doubao_cookies.json (667291 字节)】
2025-06-13 17:55:53-【web_engine.py】-【INFO】-【成功创建 doubao 流程处理器】
2025-06-13 17:55:53-【web_engine.py】-【INFO】-【成功加载网站配置: 豆包AI聊天】
2025-06-13 17:55:53-【web_engine.py】-【INFO】-【开始导航到: https://www.doubao.com/chat/】
2025-06-13 17:56:13-【web_engine.py】-【INFO】-【页面导航完成，开始等待页面完全加载...】
2025-06-13 17:56:13-【web_engine.py】-【INFO】-【等待页面加载完成，超时时间: 120秒】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【页面加载完成】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【检查登录状态...】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【检测到已登录状态】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667292 字节)】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【消息监控已启动】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【登录状态监控已启动】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【成功完成网站加载流程: https://www.doubao.com/chat/】
2025-06-13 17:56:14-【web_engine.py】-【INFO】-【页面HTML已保存到: saved_pages\doubao.html】
2025-06-13 17:56:48-【web_engine.py】-【INFO】-【使用网站特定流程发送消息并获取回复】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:56:48-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【检查侧边栏状态...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【侧边栏已关闭】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【查找新对话按钮...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【新对话按钮已禁用，说明当前处于新对话中】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【开始检查深度思考模式...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【配置选择器 - 附件上传: [data-testid="upload-file-input"]】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【配置选择器 - 状态内容: .semi-button-content-right】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【配置选择器 - 菜单项: [data-testid="dropdown-menu-item"]】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤1: 定位附件上传元素...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到附件上传元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤2: 查找深度思考按钮...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到深度思考按钮元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤3: 查找深度思考状态内容元素...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤3成功: 找到深度思考状态内容元素】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤4: 读取深度思考状态文本...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【步骤4成功: 当前深度思考状态文本: '深度思考: 自动'】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【深度思考已设置为自动模式，无需修改】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【在输入框中输入消息: 你好呀...】
2025-06-13 17:56:49-【doubao_workflow.py】-【INFO】-【点击发送按钮...】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【消息发送成功】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【消息发送成功，开始等待回复...】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【开始等待第1条消息的回复...】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【消息提取选择器配置:】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  消息列表: [data-testid="message-list"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  中间容器: [class*="inter-"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  消息容器: [class*="container-"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  接收消息: [data-testid="receive_message"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  操作栏: [class*="answer-action-bar"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  复制按钮: [data-testid="message_action_copy"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【  文本内容: [data-testid="message_text_content"]】
2025-06-13 17:56:58-【doubao_workflow.py】-【INFO】-【等待页面加载和回复开始...】
2025-06-13 17:57:01-【doubao_workflow.py】-【INFO】-【步骤1: 等待并定位消息列表...】
2025-06-13 17:57:01-【doubao_workflow.py】-【INFO】-【等待消息列表出现... 已等待 0 秒】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【步骤1成功: 找到消息列表】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【步骤2: 等待并获取回复容器...】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【目标回复容器索引: 1】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【使用的选择器:】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【  inter_container_selector: [class*="inter-"]】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【  message_container_selector: [class*="container-"]】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【  receive_message_selector: [data-testid="receive_message"]】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【查找inter-容器，选择器: [class*="inter-"]】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【找到inter-容器，查找直接子级消息容器，选择器: [class*="container-"]】
2025-06-13 17:57:02-【doubao_workflow.py】-【INFO】-【使用直接子级选择器: :scope > [class*="container-"]】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【当前消息容器数量: 2】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【检查所有 2 个容器的详细信息:】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【  容器[0]: class='container-UiWnzB', 包含receive_message: 否】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【  容器[1]: class='container-UiWnzB', 包含receive_message: 是】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【包含receive_message的容器索引: [1]】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【重新计算的目标索引: 1 (第1个回复容器)】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【✅ 使用重新计算的索引找到回复容器！】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【返回的消息容器class: 'container-UiWnzB'】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-item-Adit9A" data-target-id="message-box-target-id" data-testid="union_message"><div data-testid="messa...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【❌ 在消息容器中没有找到复制按钮】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【❌ 在消息容器中没有找到文本内容元素】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【步骤2成功: 找到回复容器】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【步骤3: 开始周期性检查回复完成状态...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【开始周期性检查回复完成状态...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:03-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:06-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:09-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:12-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:15-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【已等待 15 秒，继续等待回复完成...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:18-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:21-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:24-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:28-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:28-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:28-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:28-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:31-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【已等待 30 秒，继续等待回复完成...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:34-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:37-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:40-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:43-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:46-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【已等待 45 秒，继续等待回复完成...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:49-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:52-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:52-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:52-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:52-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:53-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:53-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:53-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:56-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:57:59-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:02-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【已等待 60 秒，继续等待回复完成...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:05-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:08-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:11-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:14-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:14-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:14-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:14-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:15-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:15-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:15-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:18-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【已等待 75 秒，继续等待回复完成...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:21-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:24-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:27-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:30-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:33-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【已等待 90 秒，继续等待回复完成...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:36-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:39-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:42-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:45-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:48-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【已等待 105 秒，继续等待回复完成...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:51-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:54-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:58:57-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:58:58-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:59:01-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【在消息容器内查找复制按钮，选择器: [data-testid="message_action_copy"]】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【❌ 复制按钮未找到，继续等待...】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【在消息容器内查找文本内容，选择器: [data-testid="message_text_content"]】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【指定选择器未找到，尝试直接从消息容器获取文本...】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【消息容器HTML结构: <div class="item-vlcIin"><div class="container-U7uO4R chrome70-container" style="--right-side-width: 0px; --left-side-width: 0px; --center-content-max-width: 848px;"><div class="inner-xy7ZZ7 inner-ite...】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【消息容器内容为空，继续等待...】
2025-06-13 17:59:04-【doubao_workflow.py】-【INFO】-【等待消息内容出现...】
2025-06-13 17:59:04-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:59:04-【web_engine.py】-【INFO】-【程序关闭前保存登录状态...】
2025-06-13 17:59:05-【web_engine.py】-【INFO】-【✅ 登录状态已保存: storage\豆包ai聊天\doubao_cookies.json (667348 字节)】
2025-06-13 17:59:05-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:59:06-【web_engine.py】-【INFO】-【浏览器引擎启动成功，无头模式: False】
2025-06-13 17:59:07-【doubao_workflow.py】-【INFO】-【已等待 120 秒，继续等待回复完成...】
2025-06-13 17:59:07-【doubao_workflow.py】-【WARNING】-【等待回复完成超时（120秒）】
2025-06-13 17:59:07-【doubao_workflow.py】-【ERROR】-【步骤3失败: 未能提取到回复内容】
2025-06-13 17:59:07-【doubao_workflow.py】-【ERROR】-【未能获取到回复】
2025-06-13 17:59:07-【main_window.py】-【INFO】-【正在关闭窗口，等待浏览器引擎停止...】
2025-06-13 17:59:07-【web_engine.py】-【INFO】-【正在停止浏览器引擎...】
2025-06-13 17:59:07-【web_engine.py】-【INFO】-【浏览器引擎已完全停止】
2025-06-13 17:59:07-【main_window.py】-【INFO】-【浏览器引擎已完全停止，窗口将关闭】
2025-06-13 17:59:07-【main.py】-【INFO】-【应用程序即将退出】
2025-06-13 17:59:07-【main.py】-【INFO】-【程序退出】
